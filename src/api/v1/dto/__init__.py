#!/usr/bin/env python3
"""
测试用例数据传输对象
"""

from typing import Optional, List

from pydantic import BaseModel, Field

class VideoAnalyzeRequest(BaseModel):
    """视频分析请求"""
    video_id: str = Field(..., description="视频会话ID")
    verification_mode: str = Field(default="step_by_step", description="验证模式: step_by_step | aggregation")

class ExpectedResult(BaseModel):
    """期望结果"""
    text: str = Field(..., description="期望结果文字描述")
    image: Optional[str] = Field("", description="base64期望结果图片，用于对比")


class StepExpectedResult(BaseModel):
    """步骤期望结果"""
    text: Optional[str] = Field(None, description="步骤期望结果文字描述")
    image: Optional[str] = Field(None, description="期望结果图片的完整文件路径，系统会自动读取并转换为base64用于模型对比")
    wait_time: float = Field(2.5, description="验证前等待时间（秒），默认2.5秒，用于等待界面加载完成")


class TestCaseStep(BaseModel):
    """测试用例步骤"""
    step: str = Field(..., description="步骤名称")
    expect_result: Optional[StepExpectedResult] = Field(None, description="每一步期望结果，非必填")


class AndroidDeviceConfig(BaseModel):
    """Android设备配置"""
    url: str = Field(..., description="adb连接地址")


class IOSDeviceConfig(BaseModel):
    """iOS设备配置"""
    # 预留iOS设备配置字段
    pass


class DeviceConfig(BaseModel):
    """设备配置"""
    type: str = Field(..., description="设备类型: android | ios")
    android: Optional[AndroidDeviceConfig] = Field(None, description="Android设备配置")
    ios: Optional[IOSDeviceConfig] = Field(None, description="iOS设备配置")
    
class UITaskStopRequest(BaseModel):
    """停止任务请求"""
    task_id: str = Field(..., description="任务ID")    


class UITaskCreateRequest(BaseModel):
    """测试用例执行请求"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="测试用例名称", min_length=1, max_length=500)
    task_expect_result: Optional[ExpectedResult] = Field(default_factory=lambda: ExpectedResult(text="", image=""), description="整个用例期望结果")
    task_step_by_step: Optional[List[TestCaseStep]] = Field(None, description="分步骤校验模式")
    task_aggregation_step: Optional[str] = Field(None, description="非分步校验模式，模型自动流转步骤")
    app_id: str = Field(..., description="执行的软件app id", min_length=1)
    agent_type: str = Field(default="android", description="android | ios，默认 android")
    agent_config_id: str = Field(default="tt", description="agent 的开放 Prompt，默认传 'tt'")
    device: DeviceConfig = Field(..., description="设备配置")

    # 新增的prompt参数化字段
    app_name: Optional[str] = Field(None, description="应用名称，如：TT语音")
    app_description: Optional[str] = Field(None, description="软件功能介绍内容，替换prompt中的软件功能介绍")
    ui_component_instructions: Optional[str] = Field(None, description="UI组件操作说明，替换prompt中的UI组件操作说明")
    special_scenarios: Optional[str] = Field(None, description="特殊场景处理说明，替换prompt中的特殊场景内容")

    # 应用重启控制字段
    is_restart: bool = Field(default=False, description="第一次执行前是否重启app，默认不重启")

    class Config:
        # 允许额外字段，提高兼容性
        extra = "ignore"

    def model_post_init(self, __context) -> None:
        """模型初始化后的验证"""
        # 确保至少有一种执行模式
        if not self.task_step_by_step and not self.task_aggregation_step:
            raise ValueError("必须提供 task_step_by_step 或 task_aggregation_step 中的一种")

        # 确保设备配置正确
        if self.device.type == "android" and not self.device.android:
            raise ValueError("Android设备类型必须提供android配置")
        elif self.device.type == "ios" and not self.device.ios:
            raise ValueError("iOS设备类型必须提供ios配置")